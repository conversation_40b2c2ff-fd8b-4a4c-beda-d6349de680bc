import sqlite3

class Database:
    """Clase para gestionar la base de datos SQLite"""

    def __init__(self, db_name="mundial.db"):
        """Inicializa la conexión a la base de datos"""
        self.db_name = db_name
        self.conn = None
        self.cursor = None
        self.connect()
        self.create_tables()

    def connect(self):
        """Establece la conexión a la base de datos"""
        try:
            self.conn = sqlite3.connect(self.db_name)
            self.conn.row_factory = sqlite3.Row  # Para acceder a las columnas por nombre
            self.cursor = self.conn.cursor()
            return True
        except sqlite3.Error as e:
            print(f"Error al conectar a la base de datos: {e}")
            return False

    def close(self):
        """Cierra la conexión a la base de datos"""
        if self.conn:
            self.conn.close()

    def commit(self):
        """Guarda los cambios en la base de datos"""
        if self.conn:
            self.conn.commit()

    def create_tables(self):
        """Crea las tablas necesarias si no existen"""
        try:
            # Tabla de torneos (con configuración de puntos para pronósticos)
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS torneos (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nombre TEXT NOT NULL,
                anio INTEGER NOT NULL,
                descripcion TEXT,
                estado TEXT DEFAULT 'Activo',
                puntos_ganador INTEGER DEFAULT 1,
                puntos_diferencia INTEGER DEFAULT 2,
                puntos_exacto INTEGER DEFAULT 3,
                UNIQUE(nombre, anio)
            )
            ''')

            # Tabla de equipos
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS equipos (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nombre TEXT NOT NULL UNIQUE,
                puntos INTEGER DEFAULT 0,
                goles_favor INTEGER DEFAULT 0,
                goles_contra INTEGER DEFAULT 0,
                diferencia_goles INTEGER DEFAULT 0,
                partidos_jugados INTEGER DEFAULT 0
            )
            ''')

            # Tabla de usuarios
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS usuarios (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                usuario TEXT NOT NULL UNIQUE,
                nombre TEXT NOT NULL,
                clave TEXT NOT NULL,
                alias TEXT NOT NULL,
                tipo TEXT DEFAULT 'Jugador'
            )
            ''')

            # Tabla de partidos
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS partidos (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                equipo_local_id INTEGER NOT NULL,
                equipo_visitante_id INTEGER NOT NULL,
                fecha TEXT NOT NULL,
                hora TEXT NOT NULL,
                torneo_id INTEGER,
                FOREIGN KEY (equipo_local_id) REFERENCES equipos (id),
                FOREIGN KEY (equipo_visitante_id) REFERENCES equipos (id),
                FOREIGN KEY (torneo_id) REFERENCES torneos (id),
                UNIQUE(equipo_local_id, equipo_visitante_id, fecha)
            )
            ''')

            # Tabla de resultados
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS resultados (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                partido_id INTEGER NOT NULL UNIQUE,
                goles_local INTEGER NOT NULL,
                goles_visitante INTEGER NOT NULL,
                fecha_registro TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (partido_id) REFERENCES partidos (id)
            )
            ''')

            self.commit()

            # Crear usuarios administradores por defecto si no existen
            self.cursor.execute("SELECT COUNT(*) as count FROM usuarios WHERE tipo = 'Administrador'")
            count = self.cursor.fetchone()['count']

            if count == 0:
                # Crear usuario admin por defecto
                self.agregar_usuario("admin", "Administrador", "admin", "Admin", "Administrador")
                print("Usuario administrador creado: usuario='admin', clave='admin'")

            # Verificar si existe el usuario Gian
            usuario_gian = self.obtener_usuario("gian")
            if usuario_gian:
                # Si existe, actualizar su tipo a Administrador
                self.actualizar_tipo_usuario("gian", "Administrador")
                print("Usuario Gian actualizado a tipo Administrador")
            else:
                # Si no existe, crearlo como Administrador
                self.agregar_usuario("gian", "Gian", "gian123", "Gian", "Administrador")
                print("Usuario administrador creado: usuario='gian', clave='gian123'")
                
            # Crear un torneo por defecto si no existe ninguno
            self.cursor.execute("SELECT COUNT(*) as count FROM torneos")
            count = self.cursor.fetchone()['count']
            
            if count == 0:
                self.agregar_torneo("CONMEBOL", 2023, "Torneo de la Confederación Sudamericana de Fútbol", "Activo")
                print("Torneo por defecto 'CONMEBOL 2023' creado")

            # Tabla de participación de usuarios en torneos
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS participaciones (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                usuario_id INTEGER NOT NULL,
                torneo_id INTEGER NOT NULL,
                puntos_pronosticos INTEGER DEFAULT 0,
                fecha_inscripcion TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (usuario_id) REFERENCES usuarios (id),
                FOREIGN KEY (torneo_id) REFERENCES torneos (id),
                UNIQUE(usuario_id, torneo_id)
            )
            ''')

            # Tabla de pronósticos
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS pronosticos (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                usuario_id INTEGER NOT NULL,
                partido_id INTEGER NOT NULL,
                goles_local_pronostico INTEGER NOT NULL,
                goles_visitante_pronostico INTEGER NOT NULL,
                puntos_obtenidos INTEGER DEFAULT 0,
                fecha_pronostico TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (usuario_id) REFERENCES usuarios (id),
                FOREIGN KEY (partido_id) REFERENCES partidos (id),
                UNIQUE(usuario_id, partido_id)
            )
            ''')

            # Verificar y añadir columna partidos_jugados si no existe
            try:
                self.cursor.execute("SELECT partidos_jugados FROM equipos LIMIT 1")
            except sqlite3.OperationalError:
                # La columna no existe, añadirla
                self.cursor.execute("ALTER TABLE equipos ADD COLUMN partidos_jugados INTEGER DEFAULT 0")
                print("Columna 'partidos_jugados' añadida a la tabla equipos")

            # Verificar y añadir columnas de puntos a torneos si no existen
            try:
                self.cursor.execute("SELECT puntos_ganador FROM torneos LIMIT 1")
            except sqlite3.OperationalError:
                # Las columnas no existen, añadirlas
                self.cursor.execute("ALTER TABLE torneos ADD COLUMN puntos_ganador INTEGER DEFAULT 1")
                self.cursor.execute("ALTER TABLE torneos ADD COLUMN puntos_diferencia INTEGER DEFAULT 2")
                self.cursor.execute("ALTER TABLE torneos ADD COLUMN puntos_exacto INTEGER DEFAULT 3")
                print("Columnas de puntos añadidas a la tabla torneos")

            return True
        except sqlite3.Error as e:
            print(f"Error al crear las tablas: {e}")
            return False

    # Funciones para equipos
    def agregar_equipo(self, nombre):
        """Agrega un nuevo equipo a la base de datos"""
        try:
            self.cursor.execute(
                "INSERT INTO equipos (nombre) VALUES (?)",
                (nombre,)
            )
            self.commit()
            return self.cursor.lastrowid
        except sqlite3.Error as e:
            print(f"Error al agregar equipo: {e}")
            return None

    def obtener_equipos(self):
        """Obtiene todos los equipos de la base de datos"""
        try:
            self.cursor.execute("SELECT * FROM equipos ORDER BY nombre")
            return self.cursor.fetchall()
        except sqlite3.Error as e:
            print(f"Error al obtener equipos: {e}")
            return []

    def obtener_equipo_por_id(self, equipo_id):
        """Obtiene un equipo por su ID"""
        try:
            self.cursor.execute("SELECT * FROM equipos WHERE id = ?", (equipo_id,))
            return self.cursor.fetchone()
        except sqlite3.Error as e:
            print(f"Error al obtener equipo: {e}")
            return None

    def obtener_equipo_por_nombre(self, nombre):
        """Obtiene un equipo por su nombre"""
        try:
            self.cursor.execute("SELECT * FROM equipos WHERE nombre = ?", (nombre,))
            return self.cursor.fetchone()
        except sqlite3.Error as e:
            print(f"Error al obtener equipo: {e}")
            return None

    def actualizar_equipo(self, equipo_id, puntos, goles_favor, goles_contra, diferencia_goles):
        """Actualiza las estadísticas de un equipo"""
        try:
            self.cursor.execute(
                """UPDATE equipos
                   SET puntos = ?, goles_favor = ?, goles_contra = ?, diferencia_goles = ?
                   WHERE id = ?""",
                (puntos, goles_favor, goles_contra, diferencia_goles, equipo_id)
            )
            self.commit()
            return True
        except sqlite3.Error as e:
            print(f"Error al actualizar equipo: {e}")
            return False

    # Funciones para usuarios
    def agregar_usuario(self, usuario, nombre, clave, alias, tipo="Jugador"):
        """Agrega un nuevo usuario a la base de datos"""
        try:
            self.cursor.execute(
                "INSERT INTO usuarios (usuario, nombre, clave, alias, tipo) VALUES (?, ?, ?, ?, ?)",
                (usuario, nombre, clave, alias, tipo)
            )
            self.commit()
            return self.cursor.lastrowid
        except sqlite3.Error as e:
            print(f"Error al agregar usuario: {e}")
            return None

    def obtener_usuarios(self):
        """Obtiene todos los usuarios de la base de datos"""
        try:
            self.cursor.execute("SELECT * FROM usuarios ORDER BY usuario")
            return self.cursor.fetchall()
        except sqlite3.Error as e:
            print(f"Error al obtener usuarios: {e}")
            return []

    def obtener_usuario(self, usuario, clave=None):
        """Obtiene un usuario por su nombre de usuario y opcionalmente su clave"""
        try:
            if clave:
                self.cursor.execute(
                    "SELECT * FROM usuarios WHERE usuario = ? AND clave = ?",
                    (usuario, clave)
                )
            else:
                self.cursor.execute("SELECT * FROM usuarios WHERE usuario = ?", (usuario,))
            return self.cursor.fetchone()
        except sqlite3.Error as e:
            print(f"Error al obtener usuario: {e}")
            return None

    def actualizar_tipo_usuario(self, usuario, tipo):
        """Actualiza el tipo de un usuario"""
        try:
            self.cursor.execute(
                "UPDATE usuarios SET tipo = ? WHERE usuario = ?",
                (tipo, usuario)
            )
            self.commit()
            return True
        except sqlite3.Error as e:
            print(f"Error al actualizar tipo de usuario: {e}")
            return False

    # Funciones para partidos
    def agregar_partido(self, equipo_local_id, equipo_visitante_id, fecha, hora, torneo_id=None):
        """Agrega un nuevo partido a la base de datos"""
        try:
            # Si no se especifica un torneo, usar el torneo activo
            if torneo_id is None:
                torneo_activo = self.obtener_torneo_activo()
                if torneo_activo:
                    torneo_id = torneo_activo['id']
                    
            self.cursor.execute(
                """INSERT INTO partidos
                   (equipo_local_id, equipo_visitante_id, fecha, hora, torneo_id)
                   VALUES (?, ?, ?, ?, ?)""",
                (equipo_local_id, equipo_visitante_id, fecha, hora, torneo_id)
            )
            self.commit()
            return self.cursor.lastrowid
        except sqlite3.Error as e:
            print(f"Error al agregar partido: {e}")
            return None

    def obtener_partidos(self, torneo_id=None):
        """Obtiene todos los partidos con nombres de equipos, opcionalmente filtrados por torneo"""
        try:
            if torneo_id:
                self.cursor.execute("""
                    SELECT p.id, p.fecha, p.hora,
                           el.id as equipo_local_id, el.nombre as equipo_local,
                           ev.id as equipo_visitante_id, ev.nombre as equipo_visitante,
                           t.nombre as torneo, t.id as torneo_id
                    FROM partidos p
                    JOIN equipos el ON p.equipo_local_id = el.id
                    JOIN equipos ev ON p.equipo_visitante_id = ev.id
                    LEFT JOIN torneos t ON p.torneo_id = t.id
                    WHERE p.torneo_id = ?
                    ORDER BY p.fecha, p.hora
                """, (torneo_id,))
            else:
                self.cursor.execute("""
                    SELECT p.id, p.fecha, p.hora,
                           el.id as equipo_local_id, el.nombre as equipo_local,
                           ev.id as equipo_visitante_id, ev.nombre as equipo_visitante,
                           t.nombre as torneo, t.id as torneo_id
                    FROM partidos p
                    JOIN equipos el ON p.equipo_local_id = el.id
                    JOIN equipos ev ON p.equipo_visitante_id = ev.id
                    LEFT JOIN torneos t ON p.torneo_id = t.id
                    ORDER BY p.fecha, p.hora
                """)
            return self.cursor.fetchall()
        except sqlite3.Error as e:
            print(f"Error al obtener partidos: {e}")
            return []

    def obtener_partidos_sin_resultado(self, torneo_id=None):
        """Obtiene los partidos que aún no tienen resultado, opcionalmente filtrados por torneo"""
        try:
            if torneo_id:
                self.cursor.execute("""
                    SELECT p.id, p.fecha, p.hora,
                           el.id as equipo_local_id, el.nombre as equipo_local,
                           ev.id as equipo_visitante_id, ev.nombre as equipo_visitante,
                           t.nombre as torneo, t.id as torneo_id
                    FROM partidos p
                    JOIN equipos el ON p.equipo_local_id = el.id
                    JOIN equipos ev ON p.equipo_visitante_id = ev.id
                    LEFT JOIN torneos t ON p.torneo_id = t.id
                    LEFT JOIN resultados r ON p.id = r.partido_id
                    WHERE r.id IS NULL AND p.torneo_id = ?
                    ORDER BY p.fecha, p.hora
                """, (torneo_id,))
            else:
                self.cursor.execute("""
                    SELECT p.id, p.fecha, p.hora,
                           el.id as equipo_local_id, el.nombre as equipo_local,
                           ev.id as equipo_visitante_id, ev.nombre as equipo_visitante,
                           t.nombre as torneo, t.id as torneo_id
                    FROM partidos p
                    JOIN equipos el ON p.equipo_local_id = el.id
                    JOIN equipos ev ON p.equipo_visitante_id = ev.id
                    LEFT JOIN torneos t ON p.torneo_id = t.id
                    LEFT JOIN resultados r ON p.id = r.partido_id
                    WHERE r.id IS NULL
                    ORDER BY p.fecha, p.hora
                """)
            return self.cursor.fetchall()
        except sqlite3.Error as e:
            print(f"Error al obtener partidos sin resultado: {e}")
            return []

    # Funciones para resultados
    def agregar_resultado(self, partido_id, goles_local, goles_visitante):
        """Agrega un nuevo resultado a la base de datos"""
        try:
            self.cursor.execute(
                "INSERT INTO resultados (partido_id, goles_local, goles_visitante) VALUES (?, ?, ?)",
                (partido_id, goles_local, goles_visitante)
            )
            self.commit()
            return self.cursor.lastrowid
        except sqlite3.Error as e:
            print(f"Error al agregar resultado: {e}")
            return None

    def obtener_resultados(self, torneo_id=None):
        """Obtiene todos los resultados con información de partidos y equipos, opcionalmente filtrados por torneo"""
        try:
            if torneo_id:
                self.cursor.execute("""
                    SELECT r.id, r.goles_local, r.goles_visitante, r.fecha_registro,
                           p.id as partido_id, p.fecha, p.hora,
                           el.id as equipo_local_id, el.nombre as equipo_local,
                           ev.id as equipo_visitante_id, ev.nombre as equipo_visitante,
                           t.nombre as torneo, t.id as torneo_id
                    FROM resultados r
                    JOIN partidos p ON r.partido_id = p.id
                    JOIN equipos el ON p.equipo_local_id = el.id
                    JOIN equipos ev ON p.equipo_visitante_id = ev.id
                    LEFT JOIN torneos t ON p.torneo_id = t.id
                    WHERE p.torneo_id = ?
                    ORDER BY p.fecha, p.hora
                """, (torneo_id,))
            else:
                self.cursor.execute("""
                    SELECT r.id, r.goles_local, r.goles_visitante, r.fecha_registro,
                           p.id as partido_id, p.fecha, p.hora,
                           el.id as equipo_local_id, el.nombre as equipo_local,
                           ev.id as equipo_visitante_id, ev.nombre as equipo_visitante,
                           t.nombre as torneo, t.id as torneo_id
                    FROM resultados r
                    JOIN partidos p ON r.partido_id = p.id
                    JOIN equipos el ON p.equipo_local_id = el.id
                    JOIN equipos ev ON p.equipo_visitante_id = ev.id
                    LEFT JOIN torneos t ON p.torneo_id = t.id
                    ORDER BY p.fecha, p.hora
                """)
            return self.cursor.fetchall()
        except sqlite3.Error as e:
            print(f"Error al obtener resultados: {e}")
            return []

    # Funciones para torneos
    def agregar_torneo(self, nombre, anio, descripcion=None, estado="Activo"):
        """Agrega un nuevo torneo a la base de datos"""
        try:
            self.cursor.execute(
                "INSERT INTO torneos (nombre, anio, descripcion, estado) VALUES (?, ?, ?, ?)",
                (nombre, anio, descripcion, estado)
            )
            self.commit()
            return self.cursor.lastrowid
        except sqlite3.Error as e:
            print(f"Error al agregar torneo: {e}")
            return None

    def obtener_torneos(self):
        """Obtiene todos los torneos de la base de datos"""
        try:
            self.cursor.execute("SELECT * FROM torneos ORDER BY anio DESC, nombre")
            return self.cursor.fetchall()
        except sqlite3.Error as e:
            print(f"Error al obtener torneos: {e}")
            return []
            
    def obtener_torneo_por_id(self, torneo_id):
        """Obtiene un torneo por su ID"""
        try:
            self.cursor.execute("SELECT * FROM torneos WHERE id = ?", (torneo_id,))
            return self.cursor.fetchone()
        except sqlite3.Error as e:
            print(f"Error al obtener torneo: {e}")
            return None
            
    def obtener_torneo_activo(self):
        """Obtiene el torneo activo más reciente"""
        try:
            self.cursor.execute(
                "SELECT * FROM torneos WHERE estado = 'Activo' ORDER BY anio DESC, id DESC LIMIT 1"
            )
            return self.cursor.fetchone()
        except sqlite3.Error as e:
            print(f"Error al obtener torneo activo: {e}")
            return None
            
    def cambiar_estado_torneo(self, torneo_id, estado):
        """Cambia el estado de un torneo (Activo/No disponible)"""
        try:
            self.cursor.execute(
                "UPDATE torneos SET estado = ? WHERE id = ?",
                (estado, torneo_id)
            )
            self.commit()
            return True
        except sqlite3.Error as e:
            print(f"Error al cambiar estado del torneo: {e}")
            return False
            
    def actualizar_torneo(self, torneo_id, nombre, anio, descripcion, estado):
        """Actualiza la información de un torneo"""
        try:
            self.cursor.execute(
                "UPDATE torneos SET nombre = ?, anio = ?, descripcion = ?, estado = ? WHERE id = ?",
                (nombre, anio, descripcion, estado, torneo_id)
            )
            self.commit()
            return True
        except sqlite3.Error as e:
            print(f"Error al actualizar torneo: {e}")
            return False

    # Funciones para calcular estadísticas
    def calcular_estadisticas(self, torneo_id=None):
        """Calcula las estadísticas de todos los equipos basado en los resultados, opcionalmente filtrados por torneo"""
        try:
            # Reiniciar estadísticas
            self.cursor.execute("""
                UPDATE equipos
                SET puntos = 0, goles_favor = 0, goles_contra = 0, diferencia_goles = 0, partidos_jugados = 0
            """)

            # Obtener todos los resultados, opcionalmente filtrados por torneo
            if torneo_id:
                self.cursor.execute("""
                    SELECT r.goles_local, r.goles_visitante,
                           p.equipo_local_id, p.equipo_visitante_id
                    FROM resultados r
                    JOIN partidos p ON r.partido_id = p.id
                    WHERE p.torneo_id = ?
                """, (torneo_id,))
            else:
                self.cursor.execute("""
                    SELECT r.goles_local, r.goles_visitante,
                           p.equipo_local_id, p.equipo_visitante_id
                    FROM resultados r
                    JOIN partidos p ON r.partido_id = p.id
                """)
            resultados = self.cursor.fetchall()

            # Procesar cada resultado
            for resultado in resultados:
                goles_local = resultado['goles_local']
                goles_visitante = resultado['goles_visitante']
                equipo_local_id = resultado['equipo_local_id']
                equipo_visitante_id = resultado['equipo_visitante_id']

                # Actualizar goles y partidos jugados
                self.cursor.execute("""
                    UPDATE equipos
                    SET goles_favor = goles_favor + ?,
                        goles_contra = goles_contra + ?,
                        partidos_jugados = partidos_jugados + 1
                    WHERE id = ?
                """, (goles_local, goles_visitante, equipo_local_id))

                self.cursor.execute("""
                    UPDATE equipos
                    SET goles_favor = goles_favor + ?,
                        goles_contra = goles_contra + ?,
                        partidos_jugados = partidos_jugados + 1
                    WHERE id = ?
                """, (goles_visitante, goles_local, equipo_visitante_id))

                # Actualizar puntos
                if goles_local > goles_visitante:
                    # Victoria local
                    self.cursor.execute("""
                        UPDATE equipos
                        SET puntos = puntos + 3
                        WHERE id = ?
                    """, (equipo_local_id,))
                elif goles_local == goles_visitante:
                    # Empate
                    self.cursor.execute("""
                        UPDATE equipos
                        SET puntos = puntos + 1
                        WHERE id IN (?, ?)
                    """, (equipo_local_id, equipo_visitante_id))
                else:
                    # Victoria visitante
                    self.cursor.execute("""
                        UPDATE equipos
                        SET puntos = puntos + 3
                        WHERE id = ?
                    """, (equipo_visitante_id,))

            # Calcular diferencia de goles
            self.cursor.execute("""
                UPDATE equipos
                SET diferencia_goles = goles_favor - goles_contra
            """)

            self.commit()
            return True
        except sqlite3.Error as e:
            print(f"Error al calcular estadísticas: {e}")
            return False

    # Funciones para gestión de pronósticos
    def inscribir_usuario_torneo(self, usuario_id, torneo_id):
        """Inscribe un usuario en un torneo"""
        try:
            self.cursor.execute("""
                INSERT OR IGNORE INTO participaciones (usuario_id, torneo_id)
                VALUES (?, ?)
            """, (usuario_id, torneo_id))
            self.commit()
            return True
        except sqlite3.Error as e:
            print(f"Error al inscribir usuario en torneo: {e}")
            return False

    def agregar_pronostico(self, usuario_id, partido_id, goles_local, goles_visitante):
        """Agrega o actualiza un pronóstico de usuario"""
        try:
            self.cursor.execute("""
                INSERT OR REPLACE INTO pronosticos
                (usuario_id, partido_id, goles_local_pronostico, goles_visitante_pronostico)
                VALUES (?, ?, ?, ?)
            """, (usuario_id, partido_id, goles_local, goles_visitante))
            self.commit()
            return True
        except sqlite3.Error as e:
            print(f"Error al agregar pronóstico: {e}")
            return False

    def obtener_pronosticos_usuario(self, usuario_id, torneo_id=None):
        """Obtiene los pronósticos de un usuario, opcionalmente filtrados por torneo"""
        try:
            if torneo_id:
                self.cursor.execute("""
                    SELECT p.*, pr.goles_local_pronostico, pr.goles_visitante_pronostico,
                           pr.puntos_obtenidos, pr.fecha_pronostico,
                           el.nombre as equipo_local, ev.nombre as equipo_visitante,
                           r.goles_local as resultado_local, r.goles_visitante as resultado_visitante
                    FROM pronosticos pr
                    JOIN partidos p ON pr.partido_id = p.id
                    JOIN equipos el ON p.equipo_local_id = el.id
                    JOIN equipos ev ON p.equipo_visitante_id = ev.id
                    LEFT JOIN resultados r ON p.id = r.partido_id
                    WHERE pr.usuario_id = ? AND p.torneo_id = ?
                    ORDER BY p.fecha, p.hora
                """, (usuario_id, torneo_id))
            else:
                self.cursor.execute("""
                    SELECT p.*, pr.goles_local_pronostico, pr.goles_visitante_pronostico,
                           pr.puntos_obtenidos, pr.fecha_pronostico,
                           el.nombre as equipo_local, ev.nombre as equipo_visitante,
                           r.goles_local as resultado_local, r.goles_visitante as resultado_visitante
                    FROM pronosticos pr
                    JOIN partidos p ON pr.partido_id = p.id
                    JOIN equipos el ON p.equipo_local_id = el.id
                    JOIN equipos ev ON p.equipo_visitante_id = ev.id
                    LEFT JOIN resultados r ON p.id = r.partido_id
                    WHERE pr.usuario_id = ?
                    ORDER BY p.fecha, p.hora
                """, (usuario_id,))
            return self.cursor.fetchall()
        except sqlite3.Error as e:
            print(f"Error al obtener pronósticos: {e}")
            return []

    def obtener_ranking_pronosticos(self, torneo_id):
        """Obtiene el ranking de usuarios por puntos de pronósticos en un torneo"""
        try:
            self.cursor.execute("""
                SELECT u.usuario, u.nombre, u.alias,
                       COALESCE(SUM(pr.puntos_obtenidos), 0) as puntos_totales,
                       COUNT(pr.id) as pronosticos_realizados
                FROM usuarios u
                JOIN participaciones pa ON u.id = pa.usuario_id
                LEFT JOIN pronosticos pr ON u.id = pr.usuario_id
                    AND pr.partido_id IN (SELECT id FROM partidos WHERE torneo_id = ?)
                WHERE pa.torneo_id = ?
                GROUP BY u.id, u.usuario, u.nombre, u.alias
                ORDER BY puntos_totales DESC, pronosticos_realizados DESC
            """, (torneo_id, torneo_id))
            return self.cursor.fetchall()
        except sqlite3.Error as e:
            print(f"Error al obtener ranking: {e}")
            return []

    def calcular_puntos_pronostico(self, partido_id):
        """Calcula los puntos de todos los pronósticos para un partido específico"""
        try:
            # Obtener el resultado real del partido
            self.cursor.execute("""
                SELECT r.goles_local, r.goles_visitante, p.torneo_id
                FROM resultados r
                JOIN partidos p ON r.partido_id = p.id
                WHERE p.id = ?
            """, (partido_id,))
            resultado = self.cursor.fetchone()

            if not resultado:
                return False

            goles_local_real = resultado['goles_local']
            goles_visitante_real = resultado['goles_visitante']
            torneo_id = resultado['torneo_id']

            # Obtener configuración de puntos del torneo
            self.cursor.execute("""
                SELECT puntos_ganador, puntos_diferencia, puntos_exacto
                FROM torneos WHERE id = ?
            """, (torneo_id,))
            config = self.cursor.fetchone()

            if not config:
                return False

            puntos_ganador = config['puntos_ganador']
            puntos_diferencia = config['puntos_diferencia']
            puntos_exacto = config['puntos_exacto']

            # Obtener todos los pronósticos para este partido
            self.cursor.execute("""
                SELECT id, usuario_id, goles_local_pronostico, goles_visitante_pronostico
                FROM pronosticos WHERE partido_id = ?
            """, (partido_id,))
            pronosticos = self.cursor.fetchall()

            # Calcular puntos para cada pronóstico
            for pronostico in pronosticos:
                goles_local_pron = pronostico['goles_local_pronostico']
                goles_visitante_pron = pronostico['goles_visitante_pronostico']
                puntos = 0

                # Resultado exacto
                if goles_local_pron == goles_local_real and goles_visitante_pron == goles_visitante_real:
                    puntos = puntos_exacto
                # Diferencia de goles correcta
                elif (goles_local_pron - goles_visitante_pron) == (goles_local_real - goles_visitante_real):
                    puntos = puntos_diferencia
                # Solo ganador correcto
                elif ((goles_local_pron > goles_visitante_pron and goles_local_real > goles_visitante_real) or
                      (goles_local_pron < goles_visitante_pron and goles_local_real < goles_visitante_real) or
                      (goles_local_pron == goles_visitante_pron and goles_local_real == goles_visitante_real)):
                    puntos = puntos_ganador

                # Actualizar puntos del pronóstico
                self.cursor.execute("""
                    UPDATE pronosticos SET puntos_obtenidos = ? WHERE id = ?
                """, (puntos, pronostico['id']))

            self.commit()
            return True
        except sqlite3.Error as e:
            print(f"Error al calcular puntos de pronóstico: {e}")
            return False
