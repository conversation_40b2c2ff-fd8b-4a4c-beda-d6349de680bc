"""
Chopoto - Sistema de gestión de partidos y resultados

Este programa permite:
1. Cargar partidos con equipos, fecha y hora
2. Cargar resultados de partidos
3. Mostrar tabla de posiciones ordenada por diferentes criterios
"""

from tkinter import *
from tkinter import ttk
from tkinter import messagebox
from datetime import date, datetime
from database import Database
from torneo import Torneo
import tkinter.font as tkFont

class ModernTheme:
    """Clase para manejar el tema moderno de la aplicación"""

    # Paleta de colores moderna
    PRIMARY = "#2563eb"      # Azul moderno
    PRIMARY_DARK = "#1d4ed8" # Azul oscuro
    PRIMARY_LIGHT = "#3b82f6" # Azul claro
    SECONDARY = "#64748b"    # Gris azulado
    SUCCESS = "#10b981"      # Verde
    WARNING = "#f59e0b"      # Amarillo/Naranja
    DANGER = "#ef4444"       # Rojo

    # Colores de fondo
    BG_PRIMARY = "#ffffff"   # Blanco
    BG_SECONDARY = "#f8fafc" # Gris muy claro
    BG_DARK = "#1e293b"      # Gris oscuro

    # Colores de texto
    TEXT_PRIMARY = "#1e293b"   # Gris oscuro
    TEXT_SECONDARY = "#64748b" # Gris medio
    TEXT_LIGHT = "#94a3b8"     # Gris claro
    TEXT_WHITE = "#ffffff"     # Blanco

    # Bordes y sombras
    BORDER = "#e2e8f0"       # Gris claro para bordes
    SHADOW = "#00000010"     # Sombra sutil

    @staticmethod
    def configure_style():
        """Configura el estilo moderno para ttk widgets"""
        style = ttk.Style()

        # Configurar tema base
        style.theme_use('clam')

        # Estilo para botones
        style.configure('Modern.TButton',
                       background=ModernTheme.PRIMARY,
                       foreground=ModernTheme.TEXT_WHITE,
                       borderwidth=0,
                       focuscolor='none',
                       padding=(20, 10))

        style.map('Modern.TButton',
                 background=[('active', ModernTheme.PRIMARY_DARK),
                           ('pressed', ModernTheme.PRIMARY_DARK)])

        # Estilo para botones secundarios
        style.configure('Secondary.TButton',
                       background=ModernTheme.BG_SECONDARY,
                       foreground=ModernTheme.TEXT_PRIMARY,
                       borderwidth=1,
                       focuscolor='none',
                       padding=(20, 10))

        # Estilo para combobox
        style.configure('Modern.TCombobox',
                       fieldbackground=ModernTheme.BG_PRIMARY,
                       borderwidth=1,
                       relief='solid')

        # Estilo para treeview
        style.configure('Modern.Treeview',
                       background=ModernTheme.BG_PRIMARY,
                       foreground=ModernTheme.TEXT_PRIMARY,
                       fieldbackground=ModernTheme.BG_PRIMARY,
                       borderwidth=0)

        style.configure('Modern.Treeview.Heading',
                       background=ModernTheme.BG_SECONDARY,
                       foreground=ModernTheme.TEXT_PRIMARY,
                       borderwidth=1,
                       relief='solid')

class Partido:
    """Clase para representar un partido"""
    def __init__(self, equipoLocal, equipoVisitante, fecha, hora):
        self.equipoLocal = equipoLocal
        self.equipoVisitante = equipoVisitante
        self.fecha = fecha
        self.hora = hora

class Resultado:
    """Clase para representar el resultado de un partido"""
    def __init__(self, equipoLocal, equipoVisitante, golesLocal, golesVisitante):
        self.equipoLocal = equipoLocal
        self.equipoVisitante = equipoVisitante
        self.golesLocal = golesLocal
        self.golesVisitante = golesVisitante

class Usuario:
    """Clase para representar un usuario del sistema"""
    def __init__(self, usuario, nombre, clave, alias, tipo="Jugador"):
        self.usuario = usuario  # Nombre de usuario para login
        self.nombre = nombre    # Nombre completo
        self.clave = clave      # Contraseña
        self.alias = alias      # Alias o apodo
        self.tipo = tipo        # Tipo de usuario: "Administrador" o "Jugador"

class Equipo:
    """Clase para representar un equipo y sus estadísticas"""
    def __init__(self, nombre, puntos=0, golesAFavor=0, golesEnContra=0, diferenciaDeGoles=0):
        self.nombre = nombre
        self.puntos = puntos
        self.golesAFavor = golesAFavor
        self.golesEnContra = golesEnContra
        self.diferenciaDeGoles = diferenciaDeGoles

class AplicacionMundial:
    """Aplicación principal para gestionar el Chopoto"""

    def __init__(self, ventana):
        self.ventana = ventana
        self.ventana.title("Chopoto - Sistema de Gestión Deportiva")
        self.ventana.geometry("1200x800")
        self.ventana.configure(bg=ModernTheme.BG_SECONDARY)

        # Configurar el ícono de la ventana (opcional)
        try:
            self.ventana.iconbitmap('icon.ico')  # Si tienes un ícono
        except:
            pass

        # Configurar estilo moderno
        ModernTheme.configure_style()

        # Configurar fuentes modernas
        self.setup_fonts()

        # Inicializar base de datos
        self.db = Database()

        # Inicializar variables
        self.usuario_actual = None
        self.barra_usuario = None  # Para la barra de información del usuario
        self.torneo_actual = None  # Para el torneo seleccionado actualmente

        # Mostrar pantalla de login
        self.mostrar_pantalla_login()

    def setup_fonts(self):
        """Configura las fuentes modernas para la aplicación"""
        self.font_title = tkFont.Font(family="Segoe UI", size=24, weight="bold")
        self.font_subtitle = tkFont.Font(family="Segoe UI", size=16, weight="bold")
        self.font_body = tkFont.Font(family="Segoe UI", size=11)
        self.font_small = tkFont.Font(family="Segoe UI", size=9)
        self.font_button = tkFont.Font(family="Segoe UI", size=11, weight="bold")

    def crear_menu(self):
        """Crea el menú de la aplicación con estilo moderno"""
        # Configurar estilo para el menú con colores modernos
        self.ventana.option_add('*Menu.font', ('Segoe UI', 11))
        self.ventana.option_add('*Menu.background', ModernTheme.PRIMARY)
        self.ventana.option_add('*Menu.foreground', ModernTheme.TEXT_WHITE)
        self.ventana.option_add('*Menu.activeBackground', ModernTheme.PRIMARY_DARK)
        self.ventana.option_add('*Menu.activeForeground', ModernTheme.TEXT_WHITE)
        self.ventana.option_add('*Menu.relief', 'flat')
        self.ventana.option_add('*Menu.borderWidth', '0')

        # Crear menú principal
        menu = Menu(self.ventana, bg=ModernTheme.PRIMARY, fg=ModernTheme.TEXT_WHITE,
                   activebackground=ModernTheme.PRIMARY_DARK, activeforeground=ModernTheme.TEXT_WHITE,
                   relief='flat', bd=0)
        self.ventana.config(menu=menu)



        # Menú de partidos
        partidos_menu = Menu(menu, tearoff=0)
        menu.add_cascade(label="Partidos", menu=partidos_menu)
        partidos_menu.add_command(label="Cargar partido", command=self.mostrar_formulario_partidos)
        partidos_menu.add_command(label="Cargar resultado", command=self.mostrar_formulario_resultados)

        # Menú de tabla
        tabla_menu = Menu(menu, tearoff=0)
        menu.add_cascade(label="Tabla", menu=tabla_menu)
        tabla_menu.add_command(label="Ordenar por puntos", command=lambda: self.ordenar_tabla("puntos"))
        tabla_menu.add_command(label="Ordenar por diferencia de goles", command=lambda: self.ordenar_tabla("diferencia"))
        tabla_menu.add_command(label="Ordenar por goles a favor", command=lambda: self.ordenar_tabla("favor"))
        tabla_menu.add_command(label="Ordenar por goles en contra", command=lambda: self.ordenar_tabla("contra"))
        tabla_menu.add_command(label="Ordenar por nombre", command=lambda: self.ordenar_tabla("nombre"))

        # Menú de usuario
        usuario_menu = Menu(menu, tearoff=0)
        menu.add_cascade(label="Usuario", menu=usuario_menu)
        usuario_menu.add_command(label="Perfil", command=self.mostrar_perfil_usuario)
        usuario_menu.add_command(label="Cerrar sesión", command=self.cerrar_sesion)

        # Menú de administración (solo visible para administradores)
        if self.usuario_actual and self.usuario_actual.tipo == "Administrador":
            admin_menu = Menu(menu, tearoff=0)
            menu.add_cascade(label="Administración", menu=admin_menu)

            # Submenú de torneos
            torneos_menu = Menu(admin_menu, tearoff=0)
            admin_menu.add_cascade(label="Torneos", menu=torneos_menu)
            torneos_menu.add_command(label="Crear torneo", command=self.mostrar_formulario_crear_torneo)
            torneos_menu.add_command(label="Editar torneo", command=self.mostrar_formulario_editar_torneo)
            torneos_menu.add_command(label="Cambiar torneo activo", command=self.mostrar_formulario_cambiar_torneo)

        # Menú de ayuda
        ayuda_menu = Menu(menu, tearoff=0)
        menu.add_cascade(label="Ayuda", menu=ayuda_menu)
        ayuda_menu.add_command(label="Acerca de", command=self.mostrar_acerca_de)
        ayuda_menu.add_separator()
        ayuda_menu.add_command(label="Salir", command=self.salir)



    def crear_tabla_posiciones(self):
        """Crea la tabla para mostrar las posiciones de los equipos con diseño moderno"""
        # Frame principal con padding moderno
        frame_tabla = Frame(self.frame_principal, bg=ModernTheme.BG_SECONDARY)
        frame_tabla.pack(fill=BOTH, expand=True, padx=20, pady=20)

        # Título de la tabla
        titulo_frame = Frame(frame_tabla, bg=ModernTheme.BG_SECONDARY)
        titulo_frame.pack(fill=X, pady=(0, 15))

        Label(titulo_frame, text="📊 Tabla de Posiciones",
              font=self.font_subtitle, bg=ModernTheme.BG_SECONDARY,
              fg=ModernTheme.TEXT_PRIMARY).pack(side=LEFT)

        # Frame para la tabla con borde moderno
        tabla_container = Frame(frame_tabla, bg=ModernTheme.BG_PRIMARY, relief='solid', bd=1)
        tabla_container.pack(fill=BOTH, expand=True)

        # Crear tabla con Treeview y estilo moderno
        columnas = ("Pos", "Equipo", "Puntos", "GF", "GC", "Dif")
        self.tabla = ttk.Treeview(tabla_container, columns=columnas, show="headings",
                                 style="Modern.Treeview")

        # Configurar encabezados con mejor diseño
        self.tabla.heading("Pos", text="Pos")
        self.tabla.heading("Equipo", text="Equipo")
        self.tabla.heading("Puntos", text="Pts")
        self.tabla.heading("GF", text="GF")
        self.tabla.heading("GC", text="GC")
        self.tabla.heading("Dif", text="Dif")

        # Configurar anchos de columnas optimizados
        self.tabla.column("Pos", width=60, anchor=CENTER)
        self.tabla.column("Equipo", width=200, anchor=W)
        self.tabla.column("Puntos", width=80, anchor=CENTER)
        self.tabla.column("GF", width=80, anchor=CENTER)
        self.tabla.column("GC", width=80, anchor=CENTER)
        self.tabla.column("Dif", width=80, anchor=CENTER)

        # Agregar scrollbar moderno
        scrollbar = ttk.Scrollbar(tabla_container, orient=VERTICAL, command=self.tabla.yview)
        self.tabla.configure(yscroll=scrollbar.set)
        scrollbar.pack(side=RIGHT, fill=Y)
        self.tabla.pack(side=LEFT, fill=BOTH, expand=True, padx=10, pady=10)

    def iniciar_aplicacion(self):
        """Inicia la aplicación después del login exitoso"""
        # Limpiar ventana
        for widget in self.ventana.winfo_children():
            widget.destroy()

        # Configurar ventana
        self.ventana.title("Chopoto")

        # Cargar el torneo activo
        torneo_db = self.db.obtener_torneo_activo()
        if torneo_db:
            self.torneo_actual = Torneo(
                id=torneo_db['id'],
                nombre=torneo_db['nombre'],
                anio=torneo_db['anio'],
                descripcion=torneo_db['descripcion'],
                estado=torneo_db['estado']
            )

        # Crear contenedor principal para organizar los elementos
        contenedor_principal = Frame(self.ventana)
        contenedor_principal.pack(fill=BOTH, expand=True)

        # Crear barra de información del usuario en la parte superior con diseño moderno
        self.barra_usuario = Frame(contenedor_principal, bg=ModernTheme.PRIMARY, height=50)
        self.barra_usuario.pack(side=TOP, fill=X)

        # Evitar que la barra cambie de tamaño
        self.barra_usuario.pack_propagate(False)

        # Mostrar información del usuario y torneo actual con mejor diseño
        if self.usuario_actual:
            # Frame para organizar la información
            info_frame = Frame(self.barra_usuario, bg=ModernTheme.PRIMARY)
            info_frame.pack(side=RIGHT, padx=20, pady=10)

            # Información del usuario
            user_info = f"👤 {self.usuario_actual.alias}"
            Label(info_frame, text=user_info, font=self.font_body,
                  bg=ModernTheme.PRIMARY, fg=ModernTheme.TEXT_WHITE).pack(side=LEFT, padx=10)

            # Separador visual
            Label(info_frame, text="•", font=self.font_body,
                  bg=ModernTheme.PRIMARY, fg=ModernTheme.TEXT_WHITE).pack(side=LEFT, padx=5)

            # Información del torneo
            if self.torneo_actual:
                torneo_info = f"🏆 {self.torneo_actual.nombre} {self.torneo_actual.anio}"
                Label(info_frame, text=torneo_info, font=self.font_body,
                      bg=ModernTheme.PRIMARY, fg=ModernTheme.TEXT_WHITE).pack(side=LEFT, padx=10)

        # Crear menú con estilo moderno
        self.crear_menu()

        # Crear frame principal (dentro del contenedor principal, debajo de la barra de usuario)
        self.frame_principal = Frame(contenedor_principal, bg="#f0f0f0")
        self.frame_principal.pack(fill=BOTH, expand=True)

        # Mostrar pantalla de bienvenida
        self.mostrar_pantalla_bienvenida()

    def mostrar_pantalla_bienvenida(self):
        """Muestra una pantalla de bienvenida moderna al usuario"""
        # Frame principal de bienvenida con diseño moderno
        frame_bienvenida = Frame(self.frame_principal, bg=ModernTheme.BG_PRIMARY,
                               relief='solid', bd=1, padx=50, pady=40)
        frame_bienvenida.place(relx=0.5, rely=0.5, anchor=CENTER, width=600, height=400)

        # Sombra simulada con frame de fondo
        shadow_frame = Frame(self.frame_principal, bg=ModernTheme.SECONDARY)
        shadow_frame.place(relx=0.5, rely=0.5, anchor=CENTER, width=605, height=405)
        frame_bienvenida.lift()

        # Logo moderno con gradiente simulado
        logo_frame = Frame(frame_bienvenida, bg=ModernTheme.PRIMARY, width=120, height=120)
        logo_frame.pack(pady=30)
        logo_frame.pack_propagate(False)

        Label(logo_frame, text="🏆", font=("Segoe UI Emoji", 40),
              bg=ModernTheme.PRIMARY, fg=ModernTheme.TEXT_WHITE).pack(expand=True)

        # Mensaje de bienvenida moderno
        Label(frame_bienvenida, text=f"¡Bienvenido, {self.usuario_actual.alias}!",
              font=self.font_title, bg=ModernTheme.BG_PRIMARY,
              fg=ModernTheme.TEXT_PRIMARY).pack(pady=(20, 10))

        Label(frame_bienvenida, text="Sistema de Gestión Deportiva Chopoto",
              font=self.font_body, bg=ModernTheme.BG_PRIMARY,
              fg=ModernTheme.TEXT_SECONDARY).pack(pady=(0, 30))

        # Botón moderno para continuar
        btn_continuar = Button(frame_bienvenida, text="Continuar →",
                              command=self.mostrar_tabla_principal,
                              bg=ModernTheme.PRIMARY, fg=ModernTheme.TEXT_WHITE,
                              font=self.font_button, relief='flat', bd=0,
                              padx=30, pady=12, cursor='hand2')
        btn_continuar.pack(pady=20)

        # Efectos hover para el botón
        def on_enter(e):
            btn_continuar.config(bg=ModernTheme.PRIMARY_DARK)
        def on_leave(e):
            btn_continuar.config(bg=ModernTheme.PRIMARY)

        btn_continuar.bind("<Enter>", on_enter)
        btn_continuar.bind("<Leave>", on_leave)

    def mostrar_tabla_principal(self):
        """Muestra la tabla de posiciones principal"""
        # Limpiar el frame principal
        for widget in self.frame_principal.winfo_children():
            widget.destroy()

        # Crear tabla de posiciones
        self.crear_tabla_posiciones()

        # Cargar datos desde archivos si existen
        self.cargar_datos_iniciales()

        # Calcular puntos y actualizar tabla
        self.calcular_puntos()
        self.actualizar_tabla_posiciones()



    def cargar_datos_iniciales(self):
        """Carga los datos iniciales desde la base de datos"""
        # No es necesario hacer nada, los datos ya están en la base de datos


    def calcular_puntos(self):
        """Calcula los puntos y estadísticas de cada equipo basado en los resultados"""
        # Usar la función de la base de datos para calcular estadísticas, filtradas por torneo si hay uno activo
        torneo_id = None
        if self.torneo_actual:
            torneo_id = self.torneo_actual.id
        self.db.calcular_estadisticas(torneo_id)

    def actualizar_tabla_posiciones(self):
        """Actualiza la tabla de posiciones en la interfaz con mejor visualización"""
        # Verificar si la tabla existe
        if not hasattr(self, 'tabla'):
            return

        # Limpiar tabla
        for item in self.tabla.get_children():
            self.tabla.delete(item)

        # Obtener equipos de la base de datos ordenados por puntos
        equipos = self.db.obtener_equipos()

        # Ordenar equipos por puntos, diferencia de goles y goles a favor
        equipos_ordenados = sorted(equipos,
                                 key=lambda x: (x['puntos'], x['diferencia_goles'], x['goles_favor']),
                                 reverse=True)

        # Agregar equipos a la tabla con posición
        for i, equipo in enumerate(equipos_ordenados, 1):
            # Formatear la diferencia de goles con signo
            diff = equipo['diferencia_goles']
            diff_str = f"+{diff}" if diff > 0 else str(diff)

            # Insertar fila con colores alternados
            item = self.tabla.insert("", END, values=(
                i,  # Posición
                f"⚽ {equipo['nombre']}",  # Nombre con emoji
                equipo['puntos'],
                equipo['goles_favor'],
                equipo['goles_contra'],
                diff_str
            ))

            # Colorear las primeras posiciones
            if i == 1:
                self.tabla.set(item, "Pos", "🥇")
            elif i == 2:
                self.tabla.set(item, "Pos", "🥈")
            elif i == 3:
                self.tabla.set(item, "Pos", "🥉")
            else:
                self.tabla.set(item, "Pos", str(i))

    def ordenar_tabla(self, criterio):
        """Ordena la tabla según el criterio especificado"""
        # Verificar si la tabla existe, si no, mostrar la tabla principal primero
        if not hasattr(self, 'tabla'):
            self.mostrar_tabla_principal()

        # Limpiar tabla
        for item in self.tabla.get_children():
            self.tabla.delete(item)

        # Obtener equipos de la base de datos con el orden adecuado
        if criterio == "puntos":
            self.cursor = self.db.conn.cursor()
            self.cursor.execute(
                "SELECT * FROM equipos ORDER BY puntos DESC, diferencia_goles DESC, goles_favor DESC"
            )
        elif criterio == "diferencia":
            self.cursor = self.db.conn.cursor()
            self.cursor.execute(
                "SELECT * FROM equipos ORDER BY diferencia_goles DESC, puntos DESC, goles_favor DESC"
            )
        elif criterio == "favor":
            self.cursor = self.db.conn.cursor()
            self.cursor.execute(
                "SELECT * FROM equipos ORDER BY goles_favor DESC, puntos DESC, diferencia_goles DESC"
            )
        elif criterio == "contra":
            self.cursor = self.db.conn.cursor()
            self.cursor.execute(
                "SELECT * FROM equipos ORDER BY goles_contra ASC"
            )
        elif criterio == "nombre":
            self.cursor = self.db.conn.cursor()
            self.cursor.execute(
                "SELECT * FROM equipos ORDER BY nombre ASC"
            )

        # Agregar equipos a la tabla
        equipos = self.cursor.fetchall()
        for equipo in equipos:
            self.tabla.insert("", END, values=(
                equipo['nombre'],
                equipo['puntos'],
                equipo['goles_favor'],
                equipo['goles_contra'],
                equipo['diferencia_goles']
            ))

    def mostrar_formulario_partidos(self):
        """Muestra el formulario moderno para cargar partidos dentro de la ventana principal"""
        # Obtener equipos de la base de datos
        equipos = self.db.obtener_equipos()

        # Verificar si hay equipos disponibles
        if not equipos:
            messagebox.showerror("Error", "No hay equipos disponibles")
            return

        # Limpiar el frame principal
        for widget in self.frame_principal.winfo_children():
            widget.destroy()

        # Crear contenedor principal con scroll
        canvas = Canvas(self.frame_principal, bg=ModernTheme.BG_SECONDARY)
        scrollbar = Scrollbar(self.frame_principal, orient="vertical", command=canvas.yview)
        scrollable_frame = Frame(canvas, bg=ModernTheme.BG_SECONDARY)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Contenedor principal dentro del scroll
        contenedor_principal = Frame(scrollable_frame, bg=ModernTheme.BG_SECONDARY)
        contenedor_principal.pack(fill=BOTH, expand=True, padx=30, pady=30)

        # Título moderno
        titulo_frame = Frame(contenedor_principal, bg=ModernTheme.BG_SECONDARY)
        titulo_frame.pack(fill=X, pady=(0, 20))

        Label(titulo_frame, text="⚽ Cargar Nuevo Partido",
              font=self.font_title, bg=ModernTheme.BG_SECONDARY,
              fg=ModernTheme.TEXT_PRIMARY).pack()

        # Frame del formulario con diseño moderno
        form_frame = Frame(contenedor_principal, bg=ModernTheme.BG_PRIMARY,
                          relief='solid', bd=1, padx=30, pady=25)
        form_frame.pack(fill=X, padx=20, pady=10)

        # Sombra simulada
        shadow_frame = Frame(contenedor_principal, bg=ModernTheme.SECONDARY)
        shadow_frame.place(in_=form_frame, x=3, y=3, relwidth=1, relheight=1)
        form_frame.lift()

        # Obtener nombres de equipos
        nombres_equipos = [equipo['nombre'] for equipo in equipos]

        # Frame para los campos del formulario
        campos_frame = Frame(form_frame, bg=ModernTheme.BG_PRIMARY)
        campos_frame.pack(fill=X, pady=15)

        # Equipo local moderno
        Label(campos_frame, text="🏠 Equipo Local", font=self.font_body,
              bg=ModernTheme.BG_PRIMARY, fg=ModernTheme.TEXT_PRIMARY).pack(anchor=W, pady=(0, 5))
        equipo_local_var = StringVar()
        equipo_local_combo = ttk.Combobox(campos_frame, textvariable=equipo_local_var,
                                         state="readonly", font=self.font_body, style="Modern.TCombobox")
        equipo_local_combo["values"] = nombres_equipos
        equipo_local_combo.pack(fill=X, pady=(0, 15), ipady=6)

        # Equipo visitante moderno
        Label(campos_frame, text="✈️ Equipo Visitante", font=self.font_body,
              bg=ModernTheme.BG_PRIMARY, fg=ModernTheme.TEXT_PRIMARY).pack(anchor=W, pady=(0, 5))
        equipo_visitante_var = StringVar()
        equipo_visitante_combo = ttk.Combobox(campos_frame, textvariable=equipo_visitante_var,
                                             state="readonly", font=self.font_body, style="Modern.TCombobox")
        equipo_visitante_combo["values"] = nombres_equipos
        equipo_visitante_combo.pack(fill=X, pady=(0, 15), ipady=6)

        # Fecha con selector moderno
        Label(campos_frame, text="📅 Fecha del Partido", font=self.font_body,
              bg=ModernTheme.BG_PRIMARY, fg=ModernTheme.TEXT_PRIMARY).pack(anchor=W, pady=(0, 5))
        frame_fecha = Frame(campos_frame, bg=ModernTheme.BG_PRIMARY)
        frame_fecha.pack(fill=X, pady=(0, 15))

        # Obtener la fecha actual
        fecha_actual = date.today()

        # Día, mes y año con spinboxes modernos (inicializados con la fecha actual)
        dia_var = StringVar(value=f"{fecha_actual.day:02d}")
        mes_var = StringVar(value=f"{fecha_actual.month:02d}")
        anio_var = StringVar(value=str(fecha_actual.year))

        # Container para los selectores de fecha
        fecha_container = Frame(frame_fecha, bg=ModernTheme.BG_SECONDARY, relief='solid', bd=1)
        fecha_container.pack(fill=X, ipady=8)

        # Spinbox para el día (1-31)
        dia_spin = Spinbox(fecha_container, from_=1, to=31, width=3, format="%02.0f",
                         textvariable=dia_var, wrap=True, font=self.font_body,
                         bg=ModernTheme.BG_PRIMARY, relief='flat', bd=0)
        dia_spin.pack(side=LEFT, padx=10)

        Label(fecha_container, text="/", font=self.font_body,
              bg=ModernTheme.BG_SECONDARY).pack(side=LEFT)

        # Spinbox para el mes (1-12)
        mes_spin = Spinbox(fecha_container, from_=1, to=12, width=3, format="%02.0f",
                         textvariable=mes_var, wrap=True, font=self.font_body,
                         bg=ModernTheme.BG_PRIMARY, relief='flat', bd=0)
        mes_spin.pack(side=LEFT, padx=10)

        Label(fecha_container, text="/", font=self.font_body,
              bg=ModernTheme.BG_SECONDARY).pack(side=LEFT)

        # Spinbox para el año (2022-2030)
        anio_spin = Spinbox(fecha_container, from_=2022, to=2030, width=5,
                          textvariable=anio_var, font=self.font_body,
                          bg=ModernTheme.BG_PRIMARY, relief='flat', bd=0)
        anio_spin.pack(side=LEFT, padx=10)

        # Hora con spinbox moderno
        Label(campos_frame, text="🕐 Hora del Partido", font=self.font_body,
              bg=ModernTheme.BG_PRIMARY, fg=ModernTheme.TEXT_PRIMARY).pack(anchor=W, pady=(0, 5))
        frame_hora = Frame(campos_frame, bg=ModernTheme.BG_PRIMARY)
        frame_hora.pack(fill=X, pady=(0, 10))

        hora_var = StringVar(value="15")
        minuto_var = StringVar(value="00")

        # Container para los selectores de hora
        hora_container = Frame(frame_hora, bg=ModernTheme.BG_SECONDARY, relief='solid', bd=1)
        hora_container.pack(fill=X, ipady=8)

        # Spinbox para la hora (0-23)
        hora_spin = Spinbox(hora_container, from_=0, to=23, width=3, format="%02.0f",
                          textvariable=hora_var, wrap=True, font=self.font_body,
                          bg=ModernTheme.BG_PRIMARY, relief='flat', bd=0)
        hora_spin.pack(side=LEFT, padx=10)

        Label(hora_container, text=":", font=self.font_body,
              bg=ModernTheme.BG_SECONDARY).pack(side=LEFT)

        # Spinbox para los minutos (0-59)
        minuto_spin = Spinbox(hora_container, from_=0, to=59, width=3, format="%02.0f",
                            textvariable=minuto_var, wrap=True, font=self.font_body,
                            bg=ModernTheme.BG_PRIMARY, relief='flat', bd=0)
        minuto_spin.pack(side=LEFT, padx=10)

        # Botones modernos
        frame_botones = Frame(form_frame, bg=ModernTheme.BG_PRIMARY)
        frame_botones.pack(pady=20, fill=X)

        # Centrar los botones
        btn_container = Frame(frame_botones, bg=ModernTheme.BG_PRIMARY)
        btn_container.pack()

        btn_guardar = Button(btn_container, text="💾 Guardar Partido",
                            command=lambda: self.guardar_partido_moderno(
                                equipo_local_var.get(),
                                equipo_visitante_var.get(),
                                f"{dia_var.get()}/{mes_var.get()}/{anio_var.get()}",
                                f"{hora_var.get()}:{minuto_var.get()}"
                            ),
                            bg=ModernTheme.SUCCESS, fg=ModernTheme.TEXT_WHITE,
                            font=self.font_button, relief='flat', bd=0,
                            padx=30, pady=12, cursor='hand2')
        btn_guardar.pack(side=LEFT, padx=(0, 15))

        btn_cancelar = Button(btn_container, text="❌ Cancelar",
                             command=self.mostrar_tabla_principal,
                             bg=ModernTheme.BG_SECONDARY, fg=ModernTheme.TEXT_PRIMARY,
                             font=self.font_body, relief='flat', bd=1,
                             padx=30, pady=10, cursor='hand2')
        btn_cancelar.pack(side=LEFT)

        # Efectos hover para los botones
        def on_enter_guardar(e):
            btn_guardar.config(bg="#059669")  # Verde más oscuro
        def on_leave_guardar(e):
            btn_guardar.config(bg=ModernTheme.SUCCESS)
        def on_enter_cancelar(e):
            btn_cancelar.config(bg=ModernTheme.BORDER)
        def on_leave_cancelar(e):
            btn_cancelar.config(bg=ModernTheme.BG_SECONDARY)

        btn_guardar.bind("<Enter>", on_enter_guardar)
        btn_guardar.bind("<Leave>", on_leave_guardar)
        btn_cancelar.bind("<Enter>", on_enter_cancelar)
        btn_cancelar.bind("<Leave>", on_leave_cancelar)

    def guardar_partido_moderno(self, equipo_local, equipo_visitante, fecha, hora):
        """Guarda un nuevo partido (versión moderna)"""
        # Validar datos
        if not equipo_local or not equipo_visitante:
            messagebox.showerror("❌ Error", "Todos los campos son obligatorios")
            return

        if equipo_local == equipo_visitante:
            messagebox.showerror("❌ Error", "El equipo local y visitante no pueden ser el mismo")
            return

        # Obtener IDs de equipos
        equipo_local_obj = self.db.obtener_equipo_por_nombre(equipo_local)
        equipo_visitante_obj = self.db.obtener_equipo_por_nombre(equipo_visitante)

        if not equipo_local_obj or not equipo_visitante_obj:
            messagebox.showerror("❌ Error", "No se encontraron los equipos seleccionados")
            return

        # Verificar si hay un torneo activo
        torneo_id = None
        if self.torneo_actual:
            torneo_id = self.torneo_actual.id

        # Guardar partido en la base de datos
        partido_id = self.db.agregar_partido(
            equipo_local_obj['id'],
            equipo_visitante_obj['id'],
            fecha,
            hora,
            torneo_id
        )

        if partido_id:
            # Mostrar mensaje de éxito y regresar a la tabla principal
            messagebox.showinfo("✅ Éxito", f"Partido guardado correctamente:\n\n🏠 {equipo_local} vs ✈️ {equipo_visitante}\n📅 {fecha} a las 🕐 {hora}")
            self.mostrar_tabla_principal()
        else:
            messagebox.showerror("❌ Error", "No se pudo guardar el partido. Puede que ya exista.")

    def guardar_partido(self, equipo_local, equipo_visitante, fecha, hora, dialogo):
        """Guarda un nuevo partido"""
        # Validar datos
        if not equipo_local or not equipo_visitante:
            messagebox.showerror("Error", "Todos los campos son obligatorios")
            return

        if equipo_local == equipo_visitante:
            messagebox.showerror("Error", "El equipo local y visitante no pueden ser el mismo")
            return

        # Obtener IDs de equipos
        equipo_local_obj = self.db.obtener_equipo_por_nombre(equipo_local)
        equipo_visitante_obj = self.db.obtener_equipo_por_nombre(equipo_visitante)

        if not equipo_local_obj or not equipo_visitante_obj:
            messagebox.showerror("Error", "No se encontraron los equipos seleccionados")
            return

        # Verificar si hay un torneo activo
        torneo_id = None
        if self.torneo_actual:
            torneo_id = self.torneo_actual.id

        # Guardar partido en la base de datos
        partido_id = self.db.agregar_partido(
            equipo_local_obj['id'],
            equipo_visitante_obj['id'],
            fecha,
            hora,
            torneo_id
        )

        if partido_id:
            # Cerrar diálogo
            messagebox.showinfo("Éxito", "Partido guardado correctamente")
            dialogo.destroy()
        else:
            messagebox.showerror("Error", "No se pudo guardar el partido. Puede que ya exista.")

    def mostrar_formulario_resultados(self):
        """Muestra el formulario para cargar resultados"""
        # Obtener partidos sin resultado de la base de datos, filtrados por torneo si hay uno activo
        torneo_id = None
        if self.torneo_actual:
            torneo_id = self.torneo_actual.id
        partidos_sin_resultado = self.db.obtener_partidos_sin_resultado(torneo_id)

        # Verificar si hay partidos sin resultado
        if not partidos_sin_resultado:
            messagebox.showerror("Error", "No hay partidos sin resultado disponibles")
            return

        # Crear ventana de diálogo
        dialogo = Toplevel(self.ventana)
        dialogo.title("Cargar Resultado")
        dialogo.geometry("400x300")
        dialogo.resizable(False, False)
        dialogo.transient(self.ventana)
        dialogo.grab_set()

        # Crear formulario
        Label(dialogo, text="Cargar Resultado", font=("Arial", 14, "bold")).pack(pady=10)

        frame = Frame(dialogo)
        frame.pack(padx=20, pady=10, fill=X)

        # Lista de partidos sin resultado
        Label(frame, text="Seleccionar Partido:").grid(row=0, column=0, sticky=W, pady=5)
        partido_var = StringVar()
        partido_combo = ttk.Combobox(frame, textvariable=partido_var, state="readonly", width=30)
        partido_combo["values"] = [f"{p['equipo_local']} vs {p['equipo_visitante']} ({p['fecha']})" for p in partidos_sin_resultado]
        # Guardar los IDs de los partidos para usarlos al guardar el resultado
        self.partidos_ids = {f"{p['equipo_local']} vs {p['equipo_visitante']} ({p['fecha']})": p['id'] for p in partidos_sin_resultado}
        partido_combo.grid(row=0, column=1, sticky=W+E, pady=5, padx=5)

        # Goles local
        Label(frame, text="Goles Local:").grid(row=1, column=0, sticky=W, pady=5)
        goles_local_var = StringVar()
        Entry(frame, textvariable=goles_local_var).grid(row=1, column=1, sticky=W+E, pady=5, padx=5)

        # Goles visitante
        Label(frame, text="Goles Visitante:").grid(row=2, column=0, sticky=W, pady=5)
        goles_visitante_var = StringVar()
        Entry(frame, textvariable=goles_visitante_var).grid(row=2, column=1, sticky=W+E, pady=5, padx=5)

        # Botones
        frame_botones = Frame(dialogo)
        frame_botones.pack(pady=20)

        Button(frame_botones, text="Guardar", command=lambda: self.guardar_resultado(
            partido_var.get(),
            goles_local_var.get(),
            goles_visitante_var.get(),
            dialogo
        )).pack(side=LEFT, padx=10)

        Button(frame_botones, text="Cancelar", command=dialogo.destroy).pack(side=LEFT, padx=10)

    def guardar_resultado(self, partido_str, goles_local_str, goles_visitante_str, dialogo):
        """Guarda un nuevo resultado"""
        # Validar datos
        if not partido_str or not goles_local_str or not goles_visitante_str:
            messagebox.showerror("Error", "Todos los campos son obligatorios")
            return

        try:
            goles_local = int(goles_local_str)
            goles_visitante = int(goles_visitante_str)

            if goles_local < 0 or goles_visitante < 0:
                raise ValueError("Los goles no pueden ser negativos")

        except ValueError:
            messagebox.showerror("Error", "Los goles deben ser números enteros positivos")
            return

        # Obtener ID del partido seleccionado
        partido_id = self.partidos_ids.get(partido_str)
        if not partido_id:
            messagebox.showerror("Error", "No se encontró el partido seleccionado")
            return

        # Guardar resultado en la base de datos
        resultado_id = self.db.agregar_resultado(partido_id, goles_local, goles_visitante)

        if resultado_id:
            # Recalcular puntos
            self.calcular_puntos()

            # Actualizar tabla si está visible
            if hasattr(self, 'tabla'):
                self.actualizar_tabla_posiciones()

            # Cerrar diálogo
            messagebox.showinfo("Éxito", "Resultado guardado correctamente")
            dialogo.destroy()
        else:
            messagebox.showerror("Error", "No se pudo guardar el resultado. Puede que ya exista.")

    def mostrar_acerca_de(self):
        """Muestra información sobre la aplicación"""
        messagebox.showinfo(
            "Acerca de",
            "Chopoto\n\n"
            "Sistema de gestión de partidos y resultados\n"
            "Versión 2.0\n\n"
            "Desarrollado para el curso de Programación"
        )

    def salir(self):
        """Cierra la aplicación"""
        # Cerrar la conexión a la base de datos
        if hasattr(self, 'db'):
            self.db.close()

        # Cerrar ventana
        self.ventana.destroy()

    def cerrar_sesion(self):
        """Cierra la sesión del usuario actual"""
        # Limpiar frame principal
        for widget in self.ventana.winfo_children():
            if widget.winfo_class() != 'Menu':
                widget.destroy()

        # Resetear usuario actual
        self.usuario_actual = None

        # Mostrar pantalla de login
        self.mostrar_pantalla_login()

    def mostrar_pantalla_login(self):
        """Muestra la pantalla de inicio de sesión"""
        # Limpiar ventana
        for widget in self.ventana.winfo_children():
            if widget.winfo_class() != 'Menu':
                widget.destroy()

        # Los equipos de la CONMEBOL ya se cargan al iniciar la aplicación

        # Configurar ventana
        self.ventana.title("Chopoto")

        # Configurar estilo para el menú
        self.ventana.option_add('*Menu.font', ('Arial', 10))
        self.ventana.option_add('*Menu.background', '#3f51b5')  # Azul índigo principal (Material Design)
        self.ventana.option_add('*Menu.foreground', 'white')
        self.ventana.option_add('*Menu.activeBackground', '#5c6bc0')  # Azul índigo claro (Material Design)
        self.ventana.option_add('*Menu.activeForeground', 'white')
        self.ventana.option_add('*Menu.relief', 'flat')

        # Crear menú de inicio de sesión
        menu = Menu(self.ventana)
        self.ventana.config(menu=menu)

        # Menú de usuario
        usuario_menu = Menu(menu, tearoff=0)
        menu.add_cascade(label="Usuario", menu=usuario_menu)
        usuario_menu.add_command(label="Iniciar Sesión", command=self.mostrar_dialogo_login)
        usuario_menu.add_command(label="Registrarse", command=self.mostrar_pantalla_registro)

        # Menú de ayuda
        ayuda_menu = Menu(menu, tearoff=0)
        menu.add_cascade(label="Ayuda", menu=ayuda_menu)
        ayuda_menu.add_command(label="Acerca de", command=self.mostrar_acerca_de)
        ayuda_menu.add_separator()
        ayuda_menu.add_command(label="Salir", command=self.salir)

        # Agregar un espacio muy grande para empujar el título al extremo derecho
        menu.add_command(label=" " * 200, state="disabled")

        # Mostrar título en el extremo derecho del menú
        menu.add_command(label="Chopoto", state="disabled", background="#5c6bc0", foreground="white")

        # Mostrar tabla vacía
        self.frame_principal = Frame(self.ventana, bg="#f0f0f0")
        self.frame_principal.pack(fill=BOTH, expand=True)

        # Mensaje de bienvenida
        Label(self.frame_principal, text="Bienvenido al Sistema de Gestión Chopoto",
              font=("Arial", 16, "bold"), bg="#f0f0f0").pack(pady=50)
        Label(self.frame_principal, text="Por favor, inicie sesión desde el menú 'Usuario' para continuar",
              font=("Arial", 12), bg="#f0f0f0").pack()

    def mostrar_dialogo_login(self):
        """Muestra el formulario de inicio de sesión moderno en la ventana principal"""
        # Limpiar ventana
        for widget in self.ventana.winfo_children():
            if widget.winfo_class() != 'Menu':
                widget.destroy()

        # Configurar ventana
        self.ventana.title("Chopoto - Iniciar Sesión")

        # Frame principal con fondo moderno
        frame_principal = Frame(self.ventana, bg=ModernTheme.BG_SECONDARY)
        frame_principal.pack(fill=BOTH, expand=True)

        # Frame para el formulario moderno (centrado) - Aumentamos el tamaño
        frame_login = Frame(frame_principal, bg=ModernTheme.BG_PRIMARY,
                           relief='solid', bd=1, padx=40, pady=30)
        frame_login.place(relx=0.5, rely=0.5, anchor=CENTER, width=500, height=500)

        # Sombra simulada
        shadow_frame = Frame(frame_principal, bg=ModernTheme.SECONDARY)
        shadow_frame.place(relx=0.5, rely=0.5, anchor=CENTER, width=505, height=505)
        frame_login.lift()

        # Logo y título modernos
        logo_frame = Frame(frame_login, bg=ModernTheme.PRIMARY, width=60, height=60)
        logo_frame.pack(pady=(0, 15))
        logo_frame.pack_propagate(False)
        Label(logo_frame, text="🔐", font=("Segoe UI Emoji", 20),
              bg=ModernTheme.PRIMARY, fg=ModernTheme.TEXT_WHITE).pack(expand=True)

        Label(frame_login, text="Iniciar Sesión", font=self.font_subtitle,
              bg=ModernTheme.BG_PRIMARY, fg=ModernTheme.TEXT_PRIMARY).pack(pady=(0, 20))

        # Frame para campos modernos
        frame_campos = Frame(frame_login, bg=ModernTheme.BG_PRIMARY)
        frame_campos.pack(fill=X, pady=(10, 20))

        # Usuario moderno
        Label(frame_campos, text="Usuario", font=self.font_body,
              bg=ModernTheme.BG_PRIMARY, fg=ModernTheme.TEXT_PRIMARY).pack(anchor=W, pady=(0, 5))
        usuario_var = StringVar()
        entrada_usuario = Entry(frame_campos, textvariable=usuario_var, width=35, font=self.font_body,
                               relief='solid', bd=1, bg=ModernTheme.BG_SECONDARY)
        entrada_usuario.pack(fill=X, pady=(0, 15), ipady=8)
        entrada_usuario.focus_set()

        # Contraseña moderna
        Label(frame_campos, text="Contraseña", font=self.font_body,
              bg=ModernTheme.BG_PRIMARY, fg=ModernTheme.TEXT_PRIMARY).pack(anchor=W, pady=(0, 5))
        clave_var = StringVar()
        entrada_clave = Entry(frame_campos, textvariable=clave_var, show="*", width=35, font=self.font_body,
                             relief='solid', bd=1, bg=ModernTheme.BG_SECONDARY)
        entrada_clave.pack(fill=X, pady=(0, 10), ipady=8)

        # Botones modernos
        frame_botones = Frame(frame_login, bg=ModernTheme.BG_PRIMARY)
        frame_botones.pack(pady=20, fill=X)

        # Centrar los botones
        btn_container = Frame(frame_botones, bg=ModernTheme.BG_PRIMARY)
        btn_container.pack()

        btn_login = Button(btn_container, text="Iniciar Sesión",
                          command=lambda: self.validar_login(usuario_var.get(), clave_var.get()),
                          bg=ModernTheme.PRIMARY, fg=ModernTheme.TEXT_WHITE,
                          font=self.font_button, relief='flat', bd=0,
                          padx=30, pady=12, cursor='hand2')
        btn_login.pack(side=LEFT, padx=(0, 15))

        btn_cancelar = Button(btn_container, text="Cancelar",
                             command=self.mostrar_pantalla_login,
                             bg=ModernTheme.BG_SECONDARY, fg=ModernTheme.TEXT_PRIMARY,
                             font=self.font_body, relief='flat', bd=1,
                             padx=30, pady=10, cursor='hand2')
        btn_cancelar.pack(side=LEFT)

        # Efectos hover
        def on_enter_login(e):
            btn_login.config(bg=ModernTheme.PRIMARY_DARK)
        def on_leave_login(e):
            btn_login.config(bg=ModernTheme.PRIMARY)
        def on_enter_cancel(e):
            btn_cancelar.config(bg=ModernTheme.BORDER)
        def on_leave_cancel(e):
            btn_cancelar.config(bg=ModernTheme.BG_SECONDARY)

        btn_login.bind("<Enter>", on_enter_login)
        btn_login.bind("<Leave>", on_leave_login)
        btn_cancelar.bind("<Enter>", on_enter_cancel)
        btn_cancelar.bind("<Leave>", on_leave_cancel)

        # Bind Enter key para login
        entrada_clave.bind('<Return>', lambda event: self.validar_login(usuario_var.get(), clave_var.get()))

    def validar_login(self, usuario, clave):
        """Valida las credenciales de inicio de sesión"""
        if not usuario or not clave:
            messagebox.showerror("Error", "Todos los campos son obligatorios")
            return

        # Buscar usuario en la base de datos
        usuario_encontrado = self.db.obtener_usuario(usuario, clave)

        if usuario_encontrado:
            # Crear objeto Usuario para mantener compatibilidad
            self.usuario_actual = Usuario(
                usuario_encontrado['usuario'],
                usuario_encontrado['nombre'],
                usuario_encontrado['clave'],
                usuario_encontrado['alias'],
                usuario_encontrado['tipo']
            )
            # Iniciar la aplicación directamente sin mostrar mensaje
            self.iniciar_aplicacion()
        else:
            messagebox.showerror("Error", "Usuario o contraseña incorrectos")

    def mostrar_pantalla_registro(self):
        """Muestra la pantalla de registro de usuario en la ventana principal"""
        # Limpiar ventana
        for widget in self.ventana.winfo_children():
            if widget.winfo_class() != 'Menu':
                widget.destroy()

        # Configurar ventana
        self.ventana.title("Chopoto - Registro de Usuario")

        # Frame principal centrado
        frame_principal = Frame(self.ventana, bg="#f0f0f0")
        frame_principal.pack(fill=BOTH, expand=True)

        # Frame para el formulario (centrado)
        frame_registro = Frame(frame_principal, bg="white", relief=RIDGE, bd=1, padx=30, pady=20)
        frame_registro.place(relx=0.5, rely=0.5, anchor=CENTER, width=450, height=350)

        # Título
        Label(frame_registro, text="Registro de Usuario", font=("Arial", 16, "bold"), bg="white").pack(pady=15)

        # Frame para campos
        frame_campos = Frame(frame_registro, bg="white")
        frame_campos.pack(fill=X, pady=10)

        # Usuario
        Label(frame_campos, text="Usuario:", bg="white", anchor=W).grid(row=0, column=0, sticky=W, pady=8)
        usuario_var = StringVar()
        entrada_usuario = Entry(frame_campos, textvariable=usuario_var, width=25, font=("Arial", 10))
        entrada_usuario.grid(row=0, column=1, sticky=W+E, pady=8, padx=10)
        # Enfocar el cursor en el campo de usuario
        entrada_usuario.focus_set()

        # Nombre
        Label(frame_campos, text="Nombre completo:", bg="white", anchor=W).grid(row=1, column=0, sticky=W, pady=8)
        nombre_var = StringVar()
        Entry(frame_campos, textvariable=nombre_var, width=25, font=("Arial", 10)).grid(row=1, column=1, sticky=W+E, pady=8, padx=10)

        # Contraseña
        Label(frame_campos, text="Contraseña:", bg="white", anchor=W).grid(row=2, column=0, sticky=W, pady=8)
        clave_var = StringVar()
        Entry(frame_campos, textvariable=clave_var, show="*", width=25, font=("Arial", 10)).grid(row=2, column=1, sticky=W+E, pady=8, padx=10)

        # Alias
        Label(frame_campos, text="Alias:", bg="white", anchor=W).grid(row=3, column=0, sticky=W, pady=8)
        alias_var = StringVar()
        Entry(frame_campos, textvariable=alias_var, width=25, font=("Arial", 10)).grid(row=3, column=1, sticky=W+E, pady=8, padx=10)

        # Tipo de usuario
        Label(frame_campos, text="Tipo de usuario:", bg="white", anchor=W).grid(row=4, column=0, sticky=W, pady=8)
        tipo_var = StringVar(value="Jugador")
        frame_tipo = Frame(frame_campos, bg="white")
        frame_tipo.grid(row=4, column=1, sticky=W+E, pady=8, padx=10)

        # Opciones de tipo de usuario
        Radiobutton(frame_tipo, text="Jugador", variable=tipo_var, value="Jugador", bg="white").pack(side=LEFT, padx=10)
        Radiobutton(frame_tipo, text="Administrador", variable=tipo_var, value="Administrador", bg="white").pack(side=LEFT, padx=10)

        # Botones
        frame_botones = Frame(frame_registro, bg="white")
        frame_botones.pack(pady=20)

        Button(frame_botones, text="Registrar", width=15,
               command=lambda: self.registrar_usuario(
                   usuario_var.get(),
                   nombre_var.get(),
                   clave_var.get(),
                   alias_var.get(),
                   tipo_var.get()
               ),
               bg="#3f51b5", fg="white", font=("Arial", 10)).pack(side=LEFT, padx=5)

        Button(frame_botones, text="Cancelar", width=10,
               command=self.mostrar_pantalla_login,
               bg="#95a5a6", fg="white", font=("Arial", 10)).pack(side=LEFT, padx=5)

    def registrar_usuario(self, usuario, nombre, clave, alias, tipo="Jugador"):
        """Registra un nuevo usuario"""
        # Validar datos
        if not usuario or not nombre or not clave or not alias:
            messagebox.showerror("Error", "Todos los campos son obligatorios")
            return

        # Verificar si el usuario ya existe
        usuario_existente = self.db.obtener_usuario(usuario)
        if usuario_existente:
            messagebox.showerror("Error", "El nombre de usuario ya está en uso")
            return

        # Crear usuario en la base de datos
        usuario_id = self.db.agregar_usuario(usuario, nombre, clave, alias, tipo)

        if usuario_id:
            # Mostrar mensaje y volver a la pantalla de login
            messagebox.showinfo("Éxito", f"Usuario registrado correctamente como {tipo}")
            self.mostrar_dialogo_login()
        else:
            messagebox.showerror("Error", "No se pudo registrar el usuario")



    def mostrar_perfil_usuario(self):
        """Muestra la información del perfil del usuario"""
        messagebox.showinfo(
            "Perfil de Usuario",
            f"Usuario: {self.usuario_actual.usuario}\n"
            f"Nombre: {self.usuario_actual.nombre}\n"
            f"Alias: {self.usuario_actual.alias}\n"
            f"Tipo: {self.usuario_actual.tipo}"
        )

    def mostrar_formulario_crear_torneo(self):
        """Muestra el formulario para crear un nuevo torneo"""
        # Crear ventana de diálogo
        dialogo = Toplevel(self.ventana)
        dialogo.title("Crear Torneo")
        dialogo.geometry("400x300")
        dialogo.resizable(False, False)
        dialogo.transient(self.ventana)
        dialogo.grab_set()

        # Crear formulario
        Label(dialogo, text="Crear Torneo", font=("Arial", 14, "bold")).pack(pady=10)

        frame = Frame(dialogo)
        frame.pack(padx=20, pady=10, fill=X)

        # Nombre del torneo
        Label(frame, text="Nombre:").grid(row=0, column=0, sticky=W, pady=5)
        nombre_var = StringVar()
        Entry(frame, textvariable=nombre_var).grid(row=0, column=1, sticky=W+E, pady=5, padx=5)

        # Año del torneo
        Label(frame, text="Año:").grid(row=1, column=0, sticky=W, pady=5)
        anio_var = StringVar(value=str(datetime.now().year))
        Entry(frame, textvariable=anio_var).grid(row=1, column=1, sticky=W+E, pady=5, padx=5)

        # Descripción del torneo
        Label(frame, text="Descripción:").grid(row=2, column=0, sticky=W, pady=5)
        descripcion_var = StringVar()
        Entry(frame, textvariable=descripcion_var).grid(row=2, column=1, sticky=W+E, pady=5, padx=5)

        # Estado del torneo
        Label(frame, text="Estado:").grid(row=3, column=0, sticky=W, pady=5)
        estado_var = StringVar(value="Activo")
        ttk.Combobox(frame, textvariable=estado_var, values=["Activo", "No disponible"], state="readonly").grid(row=3, column=1, sticky=W+E, pady=5, padx=5)

        # Botones
        frame_botones = Frame(dialogo)
        frame_botones.pack(pady=20)

        Button(frame_botones, text="Guardar", command=lambda: self.guardar_torneo(
            nombre_var.get(),
            anio_var.get(),
            descripcion_var.get(),
            estado_var.get(),
            dialogo
        )).pack(side=LEFT, padx=10)

        Button(frame_botones, text="Cancelar", command=dialogo.destroy).pack(side=LEFT, padx=10)

    def guardar_torneo(self, nombre, anio, descripcion, estado, dialogo):
        """Guarda un nuevo torneo"""
        # Validar datos
        if not nombre or not anio:
            messagebox.showerror("Error", "El nombre y el año son obligatorios")
            return

        try:
            anio = int(anio)
        except ValueError:
            messagebox.showerror("Error", "El año debe ser un número entero")
            return

        # Guardar torneo en la base de datos
        torneo_id = self.db.agregar_torneo(nombre, anio, descripcion, estado)

        if torneo_id:
            # Cerrar diálogo
            messagebox.showinfo("Éxito", "Torneo guardado correctamente")
            dialogo.destroy()

            # Si el torneo es activo, actualizar el torneo actual
            if estado == "Activo":
                torneo_db = self.db.obtener_torneo_por_id(torneo_id)
                if torneo_db:
                    self.torneo_actual = Torneo(
                        id=torneo_db['id'],
                        nombre=torneo_db['nombre'],
                        anio=torneo_db['anio'],
                        descripcion=torneo_db['descripcion'],
                        estado=torneo_db['estado']
                    )
                    # Actualizar la barra de información del usuario
                    self.actualizar_barra_usuario()
        else:
            messagebox.showerror("Error", "No se pudo guardar el torneo. Puede que ya exista.")

    def mostrar_formulario_editar_torneo(self):
        """Muestra el formulario para editar un torneo existente"""
        # Obtener torneos de la base de datos
        torneos = self.db.obtener_torneos()

        # Verificar si hay torneos disponibles
        if not torneos:
            messagebox.showerror("Error", "No hay torneos disponibles para editar")
            return

        # Crear ventana de diálogo
        dialogo = Toplevel(self.ventana)
        dialogo.title("Editar Torneo")
        dialogo.geometry("400x350")
        dialogo.resizable(False, False)
        dialogo.transient(self.ventana)
        dialogo.grab_set()

        # Crear formulario
        Label(dialogo, text="Editar Torneo", font=("Arial", 14, "bold")).pack(pady=10)

        frame = Frame(dialogo)
        frame.pack(padx=20, pady=10, fill=X)

        # Seleccionar torneo
        Label(frame, text="Seleccionar Torneo:").grid(row=0, column=0, sticky=W, pady=5)
        torneo_var = StringVar()
        torneo_combo = ttk.Combobox(frame, textvariable=torneo_var, state="readonly", width=30)
        torneo_combo["values"] = [f"{t['nombre']} {t['anio']}" for t in torneos]
        # Guardar los IDs de los torneos para usarlos al guardar
        self.torneos_ids = {f"{t['nombre']} {t['anio']}": t['id'] for t in torneos}
        torneo_combo.grid(row=0, column=1, sticky=W+E, pady=5, padx=5)

        # Variables para los campos del formulario
        nombre_var = StringVar()
        anio_var = StringVar()
        descripcion_var = StringVar()
        estado_var = StringVar()

        # Función para cargar los datos del torneo seleccionado
        def cargar_datos_torneo(event):
            torneo_seleccionado = torneo_var.get()
            if torneo_seleccionado:
                torneo_id = self.torneos_ids[torneo_seleccionado]
                torneo = self.db.obtener_torneo_por_id(torneo_id)
                if torneo:
                    nombre_var.set(torneo['nombre'])
                    anio_var.set(str(torneo['anio']))
                    descripcion_var.set(torneo['descripcion'] or "")
                    estado_var.set(torneo['estado'])

        # Asociar la función al evento de selección del combobox
        torneo_combo.bind("<<ComboboxSelected>>", cargar_datos_torneo)

        # Nombre del torneo
        Label(frame, text="Nombre:").grid(row=1, column=0, sticky=W, pady=5)
        Entry(frame, textvariable=nombre_var).grid(row=1, column=1, sticky=W+E, pady=5, padx=5)

        # Año del torneo
        Label(frame, text="Año:").grid(row=2, column=0, sticky=W, pady=5)
        Entry(frame, textvariable=anio_var).grid(row=2, column=1, sticky=W+E, pady=5, padx=5)

        # Descripción del torneo
        Label(frame, text="Descripción:").grid(row=3, column=0, sticky=W, pady=5)
        Entry(frame, textvariable=descripcion_var).grid(row=3, column=1, sticky=W+E, pady=5, padx=5)

        # Estado del torneo
        Label(frame, text="Estado:").grid(row=4, column=0, sticky=W, pady=5)
        ttk.Combobox(frame, textvariable=estado_var, values=["Activo", "No disponible"], state="readonly").grid(row=4, column=1, sticky=W+E, pady=5, padx=5)

        # Botones
        frame_botones = Frame(dialogo)
        frame_botones.pack(pady=20)

        Button(frame_botones, text="Guardar", command=lambda: self.actualizar_torneo(
            torneo_var.get(),
            nombre_var.get(),
            anio_var.get(),
            descripcion_var.get(),
            estado_var.get(),
            dialogo
        )).pack(side=LEFT, padx=10)

        Button(frame_botones, text="Cancelar", command=dialogo.destroy).pack(side=LEFT, padx=10)

    def actualizar_torneo(self, torneo_seleccionado, nombre, anio, descripcion, estado, dialogo):
        """Actualiza un torneo existente"""
        # Validar datos
        if not torneo_seleccionado:
            messagebox.showerror("Error", "Debe seleccionar un torneo")
            return

        if not nombre or not anio:
            messagebox.showerror("Error", "El nombre y el año son obligatorios")
            return

        try:
            anio = int(anio)
        except ValueError:
            messagebox.showerror("Error", "El año debe ser un número entero")
            return

        # Obtener ID del torneo
        torneo_id = self.torneos_ids[torneo_seleccionado]

        # Actualizar torneo en la base de datos
        resultado = self.db.actualizar_torneo(torneo_id, nombre, anio, descripcion, estado)

        if resultado:
            # Cerrar diálogo
            messagebox.showinfo("Éxito", "Torneo actualizado correctamente")
            dialogo.destroy()

            # Si el torneo es el actual, actualizarlo
            if self.torneo_actual and self.torneo_actual.id == torneo_id:
                torneo_db = self.db.obtener_torneo_por_id(torneo_id)
                if torneo_db:
                    self.torneo_actual = Torneo(
                        id=torneo_db['id'],
                        nombre=torneo_db['nombre'],
                        anio=torneo_db['anio'],
                        descripcion=torneo_db['descripcion'],
                        estado=torneo_db['estado']
                    )
                    # Actualizar la barra de información del usuario
                    self.actualizar_barra_usuario()

            # Si el torneo actual no está activo, cargar otro torneo activo
            if self.torneo_actual and self.torneo_actual.estado != "Activo":
                torneo_db = self.db.obtener_torneo_activo()
                if torneo_db:
                    self.torneo_actual = Torneo(
                        id=torneo_db['id'],
                        nombre=torneo_db['nombre'],
                        anio=torneo_db['anio'],
                        descripcion=torneo_db['descripcion'],
                        estado=torneo_db['estado']
                    )
                    # Actualizar la barra de información del usuario
                    self.actualizar_barra_usuario()
        else:
            messagebox.showerror("Error", "No se pudo actualizar el torneo")

    def mostrar_formulario_cambiar_torneo(self):
        """Muestra el formulario para cambiar el torneo activo"""
        # Obtener torneos de la base de datos
        torneos = self.db.obtener_torneos()

        # Verificar si hay torneos disponibles
        if not torneos:
            messagebox.showerror("Error", "No hay torneos disponibles")
            return

        # Crear ventana de diálogo
        dialogo = Toplevel(self.ventana)
        dialogo.title("Cambiar Torneo Activo")
        dialogo.geometry("400x200")
        dialogo.resizable(False, False)
        dialogo.transient(self.ventana)
        dialogo.grab_set()

        # Crear formulario
        Label(dialogo, text="Cambiar Torneo Activo", font=("Arial", 14, "bold")).pack(pady=10)

        frame = Frame(dialogo)
        frame.pack(padx=20, pady=10, fill=X)

        # Seleccionar torneo
        Label(frame, text="Seleccionar Torneo:").grid(row=0, column=0, sticky=W, pady=5)
        torneo_var = StringVar()
        torneo_combo = ttk.Combobox(frame, textvariable=torneo_var, state="readonly", width=30)
        torneo_combo["values"] = [f"{t['nombre']} {t['anio']}" for t in torneos]
        # Guardar los IDs de los torneos para usarlos al guardar
        self.torneos_ids = {f"{t['nombre']} {t['anio']}": t['id'] for t in torneos}
        torneo_combo.grid(row=0, column=1, sticky=W+E, pady=5, padx=5)

        # Botones
        frame_botones = Frame(dialogo)
        frame_botones.pack(pady=20)

        Button(frame_botones, text="Cambiar", command=lambda: self.cambiar_torneo_activo(
            torneo_var.get(),
            dialogo
        )).pack(side=LEFT, padx=10)

        Button(frame_botones, text="Cancelar", command=dialogo.destroy).pack(side=LEFT, padx=10)

    def cambiar_torneo_activo(self, torneo_seleccionado, dialogo):
        """Cambia el torneo activo"""
        # Validar datos
        if not torneo_seleccionado:
            messagebox.showerror("Error", "Debe seleccionar un torneo")
            return

        # Obtener ID del torneo
        torneo_id = self.torneos_ids[torneo_seleccionado]

        # Obtener el torneo de la base de datos
        torneo_db = self.db.obtener_torneo_por_id(torneo_id)
        if not torneo_db:
            messagebox.showerror("Error", "No se encontró el torneo seleccionado")
            return

        # Cambiar el estado del torneo a Activo
        resultado = self.db.cambiar_estado_torneo(torneo_id, "Activo")

        if resultado:
            # Actualizar el torneo actual
            self.torneo_actual = Torneo(
                id=torneo_db['id'],
                nombre=torneo_db['nombre'],
                anio=torneo_db['anio'],
                descripcion=torneo_db['descripcion'],
                estado="Activo"
            )

            # Actualizar la barra de información del usuario
            self.actualizar_barra_usuario()

            # Cerrar diálogo
            messagebox.showinfo("Éxito", f"Torneo '{torneo_seleccionado}' establecido como activo")
            dialogo.destroy()
        else:
            messagebox.showerror("Error", "No se pudo cambiar el torneo activo")

    def actualizar_barra_usuario(self):
        """Actualiza la información mostrada en la barra de usuario con diseño moderno"""
        # Verificar si la barra de usuario existe
        if not self.barra_usuario:
            return

        # Limpiar la barra de usuario
        for widget in self.barra_usuario.winfo_children():
            widget.destroy()

        # Mostrar información del usuario y torneo actual con diseño moderno
        if self.usuario_actual:
            # Frame para organizar la información
            info_frame = Frame(self.barra_usuario, bg=ModernTheme.PRIMARY)
            info_frame.pack(side=RIGHT, padx=20, pady=10)

            # Información del usuario
            user_info = f"👤 {self.usuario_actual.alias}"
            Label(info_frame, text=user_info, font=self.font_body,
                  bg=ModernTheme.PRIMARY, fg=ModernTheme.TEXT_WHITE).pack(side=LEFT, padx=10)

            # Separador visual
            Label(info_frame, text="•", font=self.font_body,
                  bg=ModernTheme.PRIMARY, fg=ModernTheme.TEXT_WHITE).pack(side=LEFT, padx=5)

            # Información del torneo
            if self.torneo_actual:
                torneo_info = f"🏆 {self.torneo_actual.nombre} {self.torneo_actual.anio}"
                Label(info_frame, text=torneo_info, font=self.font_body,
                      bg=ModernTheme.PRIMARY, fg=ModernTheme.TEXT_WHITE).pack(side=LEFT, padx=10)

    def asegurar_equipos_conmebol(self):
        """Asegura que todos los equipos de la CONMEBOL estén cargados en la base de datos"""
        # Equipos de la CONMEBOL (Confederación Sudamericana de Fútbol)
        equipos_conmebol = [
            "Argentina", "Brasil", "Uruguay", "Paraguay",
            "Chile", "Bolivia", "Perú", "Ecuador",
            "Colombia", "Venezuela"
        ]

        # Verificar y agregar cada equipo si no existe
        for nombre in equipos_conmebol:
            equipo_existente = self.db.obtener_equipo_por_nombre(nombre)
            if not equipo_existente:
                self.db.agregar_equipo(nombre)
                print(f"Equipo agregado: {nombre}")

# Iniciar aplicación
if __name__ == "__main__":
    ventana = Tk()
    app = AplicacionMundial(ventana)
    ventana.mainloop()
